import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { chat, message } from '@/lib/db/schema';
import { eq, and, asc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    // 验证聊天所有权
    const chatData = await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.id, chatId),
          eq(chat.userId, session.user.id)
        )
      )
      .limit(1);

    if (chatData.length === 0) {
      return NextResponse.json(
        { error: '聊天不存在或无权访问' },
        { status: 404 }
      );
    }

    // 获取聊天的所有消息
    const messages = await db
      .select()
      .from(message)
      .where(eq(message.chatId, chatId))
      .orderBy(asc(message.createdAt));

    return NextResponse.json({ messages });
  } catch (error) {
    console.error('获取消息列表失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;
    const body = await request.json();
    const { 
      role, 
      parts, 
      attachments = [], 
      parentMessageId,
      selectedModel,
      selectedTool,
      annotations 
    } = body;

    if (!role || !parts) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 验证聊天所有权
    const chatData = await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.id, chatId),
          eq(chat.userId, session.user.id)
        )
      )
      .limit(1);

    if (chatData.length === 0) {
      return NextResponse.json(
        { error: '聊天不存在或无权访问' },
        { status: 404 }
      );
    }

    // 创建新消息
    const newMessage = await db
      .insert(message)
      .values({
        chatId,
        role,
        parts,
        attachments,
        parentMessageId,
        selectedModel,
        selectedTool,
        annotations,
        createdAt: new Date(),
      })
      .returning();

    // 更新聊天的最后更新时间
    await db
      .update(chat)
      .set({ updatedAt: new Date() })
      .where(eq(chat.id, chatId));

    return NextResponse.json({ message: newMessage[0] });
  } catch (error) {
    console.error('创建消息失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
